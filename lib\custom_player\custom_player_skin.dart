import 'package:flutter/material.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:flutter_hls_parser/flutter_hls_parser.dart'; // Keep if needed for Variant type

import 'package:cat_tv/custom_player/player_controls.dart';
import 'package:cat_tv/custom_player/player_overlays.dart';
import 'package:cat_tv/custom_player/player_top_bar.dart';

// Define callbacks for player actions
typedef OnSeekCallback = void Function(Duration duration);
typedef OnSetVolumeCallback = void Function(double volume);
typedef OnSetRateCallback = void Function(double rate);
typedef OnSwitchQualityCallback = void Function(Variant variant);
typedef OnShowMenuCallback = void Function(BuildContext context);
typedef OnToggleVolumeSliderVisibilityCallback = void Function(bool visible);

class CustomPlayerSkin extends StatefulWidget {
  final VideoController controller;
  final bool hasSource;
  final bool showControls;
  final List<Variant> availableQualities;
  final bool isLiveStream;
  final int? bufferingPercentage;
  final String? errorMessage;
  final bool isLoading;

  // Player state data passed from parent
  final Duration currentPosition;
  final Duration totalDuration;
  final double currentVolume;
  final bool isPlaying;
  final double playbackSpeed;
  final String? currentQuality;
  final String? currentSubtitle;

  // UI state data passed from parent
  final bool isVolumeSliderVisible;
  final bool areControlsVisible; // Controls visibility managed by parent
  final bool isFullScreenMode; // New: Fullscreen mode state from parent
  final Animation<double> liveAnimation;

  // Splash effects data passed from parent
  final bool showPlayPauseSplash;
  final bool showVolumeSplash;
  final String volumeSplashText;
  final bool showCatSound;
  final String catSoundText;

  // Callbacks for user interactions
  final VoidCallback? onPlayButtonPressed;
  final VoidCallback? onTapVideo;
  final VoidCallback? onEnterControls;
  final VoidCallback? onExitControls;
  final OnSeekCallback? onSeek;
  final OnSetVolumeCallback? onSetVolume;
  final VoidCallback? onToggleMute;
  final OnSetRateCallback? onSetRate;
  final OnSwitchQualityCallback? onSwitchQuality;
  final OnShowMenuCallback? onShowSubtitlesMenu;
  final OnShowMenuCallback? onShowSettingsMenu;
  final VoidCallback? onEnterPictureInPicture;
  final VoidCallback? onStartChromecast;
  final VoidCallback? onShowCatSoundEffect;
  final OnToggleVolumeSliderVisibilityCallback? onToggleVolumeSliderVisibility;
  final VoidCallback? onToggleFullScreen; // New: Callback for fullscreen toggle

  const CustomPlayerSkin({
    super.key,
    required this.controller,
    required this.hasSource,
    required this.showControls, // This will now be `areControlsVisible` from parent
    this.availableQualities = const [],
    this.isLiveStream = true,
    this.bufferingPercentage,
    this.errorMessage,
    this.isLoading = false,
    // Player state data
    required this.currentPosition,
    required this.totalDuration,
    required this.currentVolume,
    required this.isPlaying,
    required this.playbackSpeed,
    this.currentQuality,
    this.currentSubtitle,
    // UI state data
    required this.isVolumeSliderVisible,
    required this.areControlsVisible,
    required this.isFullScreenMode, // Initialize new state
    required this.liveAnimation,
    // Splash effects data
    required this.showPlayPauseSplash,
    required this.showVolumeSplash,
    required this.volumeSplashText,
    required this.showCatSound,
    required this.catSoundText,
    // Callbacks
    this.onPlayButtonPressed,
    this.onTapVideo,
    this.onEnterControls,
    this.onExitControls,
    this.onSeek,
    this.onSetVolume,
    this.onToggleMute,
    this.onSetRate,
    this.onSwitchQuality,
    this.onShowSubtitlesMenu,
    this.onShowSettingsMenu,
    this.onEnterPictureInPicture,
    this.onStartChromecast,
    this.onShowCatSoundEffect,
    this.onToggleVolumeSliderVisibility,
    this.onToggleFullScreen, // Initialize new callback
  });

  @override
  State<CustomPlayerSkin> createState() => _CustomPlayerSkinState();
}

class _CustomPlayerSkinState extends State<CustomPlayerSkin> {
  @override
  void dispose() {
    // No need to call _exitFullScreen here, as PlayerPage manages SystemChrome
    super.dispose();
  }

  void _toggleFullScreenInternal() {
    widget.onToggleFullScreen?.call();
  }

  @override
  Widget build(BuildContext context) {
    return MediaQuery(
      data: MediaQuery.of(context).copyWith(
        padding: widget.isFullScreenMode ? EdgeInsets.zero : null,
        viewInsets: widget.isFullScreenMode ? EdgeInsets.zero : null,
        viewPadding: widget.isFullScreenMode ? EdgeInsets.zero : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: MouseRegion(
          onEnter: (_) => widget.onEnterControls?.call(),
          onHover: (_) => widget.onEnterControls?.call(),
          onExit: (_) => widget.onExitControls?.call(),
          child: GestureDetector(
            onTap: () => widget.onTapVideo?.call(),
            child: Stack(
              children: [
                SizedBox.expand(
                  child: Video(controller: widget.controller, controls: null),
                ),
                PlayerOverlays(
                  hasSource: widget.hasSource,
                  isPlaying: widget.isPlaying,
                  isLoading: widget.isLoading,
                  bufferingPercentage: widget.bufferingPercentage,
                  errorMessage: widget.errorMessage,
                  showPlayPauseSplash: widget.showPlayPauseSplash,
                  showVolumeSplash: widget.showVolumeSplash,
                  volumeSplashText: widget.volumeSplashText,
                  showCatSound: widget.showCatSound,
                  catSoundText: widget.catSoundText,
                  areControlsVisible: widget.areControlsVisible,
                  onPlayButtonPressed: widget.onPlayButtonPressed,
                ),
                PlayerTopBar(
                  hasSource: widget.hasSource,
                  onShowCatSoundEffect: widget.onShowCatSoundEffect,
                  onStartChromecast: widget.onStartChromecast,
                ),
                PlayerControls(
                  hasSource: widget.hasSource,
                  areControlsVisible: widget.areControlsVisible,
                  isLiveStream: widget.isLiveStream,
                  liveAnimation: widget.liveAnimation,
                  currentPosition: widget.currentPosition,
                  totalDuration: widget.totalDuration,
                  currentVolume: widget.currentVolume,
                  isPlaying: widget.isPlaying,
                  playbackSpeed: widget.playbackSpeed,
                  currentQuality: widget.currentQuality,
                  currentSubtitle: widget.currentSubtitle,
                  isVolumeSliderVisible: widget.isVolumeSliderVisible,
                  isFullScreenMode: widget.isFullScreenMode,
                  onPlayButtonPressed: widget.onPlayButtonPressed,
                  onSeek: widget.onSeek,
                  onSetVolume: widget.onSetVolume,
                  onToggleMute: widget.onToggleMute,
                  onSetRate: widget.onSetRate,
                  onSwitchQuality: widget.onSwitchQuality,
                  onShowSubtitlesMenu: widget.onShowSubtitlesMenu,
                  onShowSettingsMenu: widget.onShowSettingsMenu,
                  onEnterPictureInPicture: widget.onEnterPictureInPicture,
                  onToggleFullScreen: _toggleFullScreenInternal,
                  onToggleVolumeSliderVisibility:
                      widget.onToggleVolumeSliderVisibility,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
