import 'package:cat_tv/services/favorites_service.dart';

class Channel {
  final String channelId;
  final String name;
  final List<String> altNames;
  final String countryCode;
  final int? categoryId;
  final String? logoUrl;
  final bool isActive;
  final bool isExternal;
  final List<String> sources;
  bool _isFavorite; // Private field for favorite status

  Channel({
    required this.channelId,
    required this.name,
    required this.altNames,
    required this.countryCode,
    this.categoryId,
    this.logoUrl,
    required this.isActive,
    required this.isExternal,
    required this.sources,
    bool isFavorite = false, // Default to false
  }) : _isFavorite = isFavorite;

  // Factory constructor to create a Channel from a map WITHOUT checking favorites
  // This prevents the infinite loop when loading favorites
  static Channel fromMapSync(
    Map<String, dynamic> map, {
    bool isFavorite = false,
  }) {
    return Channel(
      channelId: map['channel_id'],
      name: map['name'],
      altNames: (map['alt_names'] as String?)?.split(',') ?? [],
      countryCode: map['country_code'],
      categoryId: map['category_id'],
      logoUrl: map['logo_url'],
      isActive: map['is_active'] == 1,
      isExternal: map['is_external'] == 1,
      sources: (map['sources'] as String?)?.split(',') ?? [],
      isFavorite: isFavorite,
    );
  }

  // Factory constructor to create a Channel from a map, including favorite status
  // Use this for general channel loading (not when loading favorites)
  static Future<Channel> fromMap(Map<String, dynamic> map) async {
    final channel = Channel.fromMapSync(map);
    // Set favorite status after creation
    channel._isFavorite = await FavoritesService.isFavoriteChannel(
      channel.channelId,
    );
    return channel;
  }

  // Getter for favorite status
  bool get isFavorite => _isFavorite;

  // Method to toggle favorite status
  Future<void> toggleFavorite() async {
    if (_isFavorite) {
      await FavoritesService.removeFavoriteChannel(channelId);
    } else {
      await FavoritesService.addFavoriteChannel(channelId);
    }
    _isFavorite = !_isFavorite; // Update local state
  }

  // Method to update favorite status without toggling
  void setFavoriteStatus(bool isFavorite) {
    _isFavorite = isFavorite;
  }

  String get channelNameOrDefault {
    return name.isNotEmpty ? name : 'Unknown Channel';
  }

  String get altNamesOrDefault {
    return altNames.isNotEmpty ? altNames.join(', ') : 'No alternative names';
  }

  String get logoUrlOrDefault {
    return logoUrl ?? 'https://static.thenounproject.com/png/3548799-200.png';
  }

  String get categoryIdOrDefault {
    return categoryId?.toString() ?? 'No category';
  }

  String get countryCodeOrDefault {
    return countryCode.isNotEmpty ? countryCode : 'No country code';
  }

  get isActiveOrDefault {
    return isActive ? 'Active' : 'Inactive';
  }

  // Add url getter for backward compatibility
  String get url => sources.isNotEmpty ? sources.first : '';
}
