import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:ui';
import '../controllers/filter_controller.dart' as filter_ctrl;
import 'package:cat_tv/models/region.dart';
import 'package:cat_tv/models/country.dart';
import 'package:cat_tv/models/language.dart';
import 'package:cat_tv/models/category.dart' as cat_model;

class FilterWidget extends StatefulWidget {
  final filter_ctrl.FilterController controller;
  final List<Region> regions;
  final List<Country> countries;
  final List<Language> languages;
  final List<cat_model.Category> categories;

  const FilterWidget({
    super.key,
    required this.controller,
    required this.regions,
    required this.countries,
    required this.languages,
    required this.categories,
  });

  @override
  State<FilterWidget> createState() => _FilterWidgetState();
}

class _FilterWidgetState extends State<FilterWidget> {
  late TextEditingController _searchController;
  Timer? _debounce;
  bool _isFilterExpanded = false; // New state variable

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController(
      text: widget.controller.filter.name,
    );
    widget.controller.addListener(_onFilterChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _debounce?.cancel();
    widget.controller.removeListener(_onFilterChanged);
    super.dispose();
  }

  void _onFilterChanged() {
    if (_searchController.text != widget.controller.filter.name) {
      _searchController.text = widget.controller.filter.name ?? '';
    }
  }

  void _onSearchChanged(String value) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      final newValue = value.isEmpty ? null : value;
      widget.controller.setNameFilter(newValue);
    });
  }

  Widget _buildDropdown<T>(
    BuildContext context,
    String hintText,
    T? value,
    List<DropdownMenuItem<T>> items,
    ValueChanged<T?> onChanged,
    String allOptionText,
    bool shouldExpand,
  ) {
    return DropdownButtonFormField<T>(
      isExpanded: shouldExpand,
      value: value,
      decoration: InputDecoration(
        labelText: hintText,
        labelStyle: const TextStyle(color: Colors.white70),
        filled: true,
        fillColor: Colors.white.withOpacity(0.1),
        contentPadding:
            shouldExpand
                ? const EdgeInsets.symmetric(horizontal: 12, vertical: 16)
                : const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.white.withOpacity(0.2)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.white.withOpacity(0.2)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Theme.of(context).colorScheme.primary),
        ),
      ), // Corrected closing parenthesis for InputDecoration
      dropdownColor: Colors.black.withOpacity(
        0.8,
      ), // More opaque for better readability
      style: const TextStyle(color: Colors.white, fontSize: 14),
      iconEnabledColor: Colors.white,
      items: [
        DropdownMenuItem<T>(
          value: null,
          child: Text(
            allOptionText,
            style: const TextStyle(color: Colors.white),
          ),
        ),
        ...items,
      ],
      onChanged: onChanged,
    );
  }

  Widget _buildResetButton(BuildContext context, bool isMobile) {
    return ElevatedButton.icon(
      onPressed: () {
        widget.controller.resetAll();
      },
      icon: const Icon(Icons.refresh, size: 20),
      label: Text('Reset', style: TextStyle(fontSize: isMobile ? 14 : 16)),
      style: ElevatedButton.styleFrom(
        minimumSize:
            isMobile ? const Size.fromHeight(40) : const Size.fromHeight(48),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        backgroundColor: Colors.white.withOpacity(0.1),
        foregroundColor: Colors.white,
        elevation: 0,
        side: BorderSide(color: Colors.white.withOpacity(0.2)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 600;
    final topPadding = MediaQuery.of(context).padding.top;

    return AnimatedBuilder(
      animation: widget.controller,
      builder: (context, _) {
        return Padding(
          padding: EdgeInsets.only(
            left: isMobile ? 8 : 12,
            right: isMobile ? 8 : 12,
            bottom: isMobile ? 4 : 8,
            top: topPadding + (isMobile ? 4 : 8),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(isMobile ? 12 : 16),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(isMobile ? 12 : 16),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.2),
                    width: 1.0,
                  ),
                  gradient: const LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color.fromARGB(255, 30, 30, 30),
                      Color.fromARGB(255, 60, 0, 60),
                    ],
                  ),
                ),
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: isMobile ? 12 : 16,
                    vertical: isMobile ? 8 : 10,
                  ),
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _SearchField(
                          controller: _searchController,
                          filterController: widget.controller,
                          onSearchChanged: _onSearchChanged,
                          onSearchSubmitted: (value) {
                            _debounce?.cancel();
                            final newValue = value.isEmpty ? null : value;
                            widget.controller.setNameFilter(newValue);
                          },
                          isMobile: isMobile,
                        ),
                        const SizedBox(height: 12),
                        if (isMobile)
                          ExpansionTile(
                            key: const PageStorageKey('filterExpansionTile'),
                            initiallyExpanded: _isFilterExpanded,
                            onExpansionChanged: (expanded) {
                              setState(() {
                                _isFilterExpanded = expanded;
                              });
                            },
                            title: Text(
                              _isFilterExpanded
                                  ? 'Hide Filters'
                                  : 'Show Filters',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                              ),
                            ),
                            tilePadding: EdgeInsets.zero,
                            childrenPadding: EdgeInsets.zero,
                            children: [_buildMobileLayout(context)],
                          )
                        else
                          _buildDesktopLayout(context),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        _buildDropdown(
          context,
          'Category',
          widget.controller.filter.categoryId,
          widget.categories
              .map(
                (cat) => DropdownMenuItem(
                  value: cat.id,
                  child: Text(
                    cat.name,
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              )
              .toList(),
          (val) {
            if (val == null) {
              widget.controller.clearFilter(category: true);
            } else {
              widget.controller.setFilter(categoryId: val);
            }
          },
          'All Categories',
          true,
        ),
        const SizedBox(height: 8),
        _buildDropdown(
          context,
          'Region',
          widget.controller.filter.region,
          widget.regions
              .map(
                (r) => DropdownMenuItem(
                  value: r.code,
                  child: Text(
                    r.name,
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              )
              .toList(),
          (val) {
            if (val == null) {
              widget.controller.clearFilter(region: true);
            } else {
              widget.controller.setFilter(region: val, country: null);
            }
          },
          'All Regions',
          true,
        ),
        const SizedBox(height: 8),
        _buildDropdown(
          context,
          'Country',
          widget.controller.filter.country,
          widget.countries
              .map(
                (c) => DropdownMenuItem(
                  value: c.code,
                  child: Row(
                    children: [
                      if (c.flag.isNotEmpty) Text(c.flag),
                      if (c.flag.isNotEmpty) const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          c.name,
                          style: const TextStyle(color: Colors.white),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              )
              .toList(),
          (val) {
            if (val == null) {
              widget.controller.clearFilter(country: true);
            } else {
              widget.controller.setFilter(country: val);
            }
          },
          'All Countries',
          true,
        ),
        const SizedBox(height: 8),
        _buildDropdown(
          context,
          'Language',
          widget.controller.filter.language,
          widget.languages
              .map(
                (l) => DropdownMenuItem(
                  value: l.code,
                  child: Text(
                    l.name,
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              )
              .toList(),
          (val) {
            if (val == null) {
              widget.controller.clearFilter(language: true);
            } else {
              widget.controller.setFilter(language: val);
            }
          },
          'All Languages',
          true,
        ),
        const SizedBox(height: 12),
        _buildResetButton(context, true),
      ],
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return Column(
      children: [
        Wrap(
          spacing: 12.0,
          runSpacing: 12.0,
          alignment: WrapAlignment.start,
          children: [
            SizedBox(
              width: 200,
              child: _buildDropdown(
                context,
                'Category',
                widget.controller.filter.categoryId,
                widget.categories
                    .map(
                      (cat) => DropdownMenuItem(
                        value: cat.id,
                        child: Text(
                          cat.name,
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                    )
                    .toList(),
                (val) {
                  if (val == null) {
                    widget.controller.clearFilter(category: true);
                  } else {
                    widget.controller.setFilter(categoryId: val);
                  }
                },
                'All Categories',
                true,
              ),
            ),
            SizedBox(
              width: 200,
              child: _buildDropdown(
                context,
                'Region',
                widget.controller.filter.region,
                widget.regions
                    .map(
                      (r) => DropdownMenuItem(
                        value: r.code,
                        child: Text(
                          r.name,
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                    )
                    .toList(),
                (val) {
                  if (val == null) {
                    widget.controller.clearFilter(region: true);
                  } else {
                    widget.controller.setFilter(region: val, country: null);
                  }
                },
                'All Regions',
                true,
              ),
            ),
            SizedBox(
              width: 200,
              child: _buildDropdown(
                context,
                'Country',
                widget.controller.filter.country,
                widget.countries
                    .map(
                      (c) => DropdownMenuItem(
                        value: c.code,
                        child: Row(
                          children: [
                            if (c.flag.isNotEmpty) Text(c.flag),
                            if (c.flag.isNotEmpty) const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                c.name,
                                style: const TextStyle(color: Colors.white),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                    .toList(),
                (val) {
                  if (val == null) {
                    widget.controller.clearFilter(country: true);
                  } else {
                    widget.controller.setFilter(country: val);
                  }
                },
                'All Countries',
                true,
              ),
            ),
            SizedBox(
              width: 200,
              child: _buildDropdown(
                context,
                'Language',
                widget.controller.filter.language,
                widget.languages
                    .map(
                      (l) => DropdownMenuItem(
                        value: l.code,
                        child: Text(
                          l.name,
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                    )
                    .toList(),
                (val) {
                  if (val == null) {
                    widget.controller.clearFilter(language: true);
                  } else {
                    widget.controller.setFilter(language: val);
                  }
                },
                'All Languages',
                true,
              ),
            ),
            _buildResetButton(context, false),
          ],
        ),
      ],
    );
  }
}

class _SearchField extends StatelessWidget {
  final TextEditingController controller;
  final filter_ctrl.FilterController filterController;
  final ValueChanged<String> onSearchChanged;
  final ValueChanged<String> onSearchSubmitted;
  final bool isMobile;

  const _SearchField({
    required this.controller,
    required this.filterController,
    required this.onSearchChanged,
    required this.onSearchSubmitted,
    required this.isMobile,
  });

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      style: TextStyle(color: Colors.white, fontSize: isMobile ? 14 : 16),
      decoration: InputDecoration(
        labelText: 'Search by name',
        labelStyle: TextStyle(
          color: Colors.white70,
          fontSize: isMobile ? 14 : 16,
        ),
        hintText: 'Enter channel name',
        hintStyle: TextStyle(
          color: Colors.white54,
          fontSize: isMobile ? 14 : 16,
        ),
        prefixIcon: const Icon(Icons.search),
        prefixIconColor: Colors.white70,
        suffixIcon:
            filterController.filter.name != null &&
                    filterController.filter.name!.isNotEmpty
                ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    filterController.setNameFilter(null);
                  },
                  color: Colors.white70,
                )
                : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.white.withOpacity(0.2)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.white.withOpacity(0.2)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Theme.of(context).colorScheme.primary),
        ),
        filled: true,
        fillColor: Colors.white.withOpacity(0.1),
        contentPadding:
            isMobile
                ? const EdgeInsets.symmetric(vertical: 12, horizontal: 16)
                : const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      ),
      onChanged: onSearchChanged,
      onSubmitted: onSearchSubmitted,
    );
  }
}
