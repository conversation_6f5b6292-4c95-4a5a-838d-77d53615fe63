import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:country_flags/country_flags.dart';
import 'package:cat_tv/models/channel.dart';
import 'package:cat_tv/utils/display_mode.dart';
import 'package:flutter/foundation.dart'; // For kDebugMode

class ChannelCardWidget extends StatelessWidget {
  final Channel channel;
  final DisplayMode mode;
  final Future<void> Function(BuildContext, Channel) onChannelTap;
  final VoidCallback onToggleFavorite;

  const ChannelCardWidget({
    super.key,
    required this.channel,
    required this.mode,
    required this.onChannelTap,
    required this.onToggleFavorite,
  });

  @override
  Widget build(BuildContext context) {
    final bool isList = mode == DisplayMode.list;
    final bool isSmallTiles = mode == DisplayMode.smallTiles;
    final double imageSize = isList ? 40 : 40;
    final double fontSize = isList ? 14 : 10;
    final double favoriteIconSize = isList ? 24 : 20;
    final double padding = isList ? 8.0 : 4.0;
    final double borderRadius = isList ? 12.0 : 8.0;

    return Card(
      elevation: 4.0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      color: Colors.transparent,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(borderRadius),
          border: Border.all(color: Colors.white.withOpacity(0.2), width: 1.0),
        ),
        child: InkWell(
          borderRadius: BorderRadius.circular(borderRadius),
          onTap: () async {
            await onChannelTap(context, channel);
          },
          child: Padding(
            padding: EdgeInsets.all(padding),
            child:
                isList
                    ? Row(
                      children: [
                        channel.logoUrl != null && channel.logoUrl!.isNotEmpty
                            ? ClipRRect(
                              borderRadius: BorderRadius.circular(8.0),
                              child: CachedNetworkImage(
                                imageUrl: channel.logoUrl!,
                                width: imageSize,
                                height: imageSize,
                                fit: BoxFit.contain,
                                placeholder:
                                    (context, url) => Icon(
                                      Icons.tv,
                                      size: imageSize,
                                      color: Colors.grey,
                                    ),
                                errorWidget: (context, url, error) {
                                  if (kDebugMode) {
                                    print(
                                      'couldn\'t load logo of channel ${channel.name}: $error',
                                    );
                                  }
                                  return Icon(
                                    Icons.image_not_supported,
                                    size: imageSize,
                                    color: Colors.grey,
                                  );
                                },
                              ),
                            )
                            : Icon(
                              Icons.tv,
                              size: imageSize,
                              color: Colors.grey,
                            ),
                        const SizedBox(width: 8.0),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                channel.name,
                                style: TextStyle(
                                  fontSize: fontSize,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              Row(
                                children: [
                                  CountryFlag.fromCountryCode(
                                    channel.countryCode,
                                    height: fontSize * 1.2,
                                    width: fontSize * 1.6,
                                    borderRadius: 4,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    channel.countryCode,
                                    style: TextStyle(
                                      fontSize: fontSize * 0.8,
                                      color: Colors.white70,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  const SizedBox(width: 8),
                                ],
                              ),
                            ],
                          ),
                        ),
                        IconButton(
                          icon: Icon(
                            channel.isFavorite
                                ? Icons.favorite
                                : Icons.favorite_border,
                            color:
                                channel.isFavorite
                                    ? Theme.of(context).colorScheme.primary
                                    : Colors.white70,
                            size: favoriteIconSize,
                          ),
                          onPressed: onToggleFavorite,
                        ),
                      ],
                    )
                    : Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        channel.logoUrl != null && channel.logoUrl!.isNotEmpty
                            ? ClipRRect(
                              borderRadius: BorderRadius.circular(8.0),
                              child: CachedNetworkImage(
                                imageUrl: channel.logoUrl!,
                                width: imageSize,
                                height: imageSize,
                                fit: BoxFit.contain,
                                placeholder:
                                    (context, url) => Icon(
                                      Icons.tv,
                                      size: imageSize,
                                      color: Colors.grey,
                                    ),
                                errorWidget: (context, url, error) {
                                  if (kDebugMode) {
                                    print(
                                      'couldn\'t load logo of channel ${channel.name}: $error',
                                    );
                                  }
                                  return Icon(
                                    Icons.image_not_supported,
                                    size: imageSize,
                                    color: Colors.grey,
                                  );
                                },
                              ),
                            )
                            : Icon(
                              Icons.tv,
                              size: imageSize,
                              color: Colors.grey,
                            ),
                        SizedBox(height: isSmallTiles ? 4.0 : 8.0),
                        Text(
                          channel.name,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: fontSize,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (isList) ...[
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CountryFlag.fromCountryCode(
                                channel.countryCode,
                                height: fontSize * 1.2,
                                width: fontSize * 1.6,
                                borderRadius: 4,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                channel.countryCode,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontSize: fontSize * 0.8,
                                  color: Colors.white70,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                        ],
                        IconButton(
                          icon: Icon(
                            channel.isFavorite
                                ? Icons.favorite
                                : Icons.favorite_border,
                            color:
                                channel.isFavorite
                                    ? Theme.of(context).colorScheme.primary
                                    : Colors.white70,
                            size: favoriteIconSize,
                          ),
                          onPressed: onToggleFavorite,
                        ),
                      ],
                    ),
          ),
        ),
      ),
    );
  }
}
