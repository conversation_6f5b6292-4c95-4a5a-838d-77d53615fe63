import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // Required for SystemChrome
import 'dart:ui'; // Required for ImageFilter
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:cat_tv/models/channel.dart';
import 'package:cat_tv/utils/window_manager_windows.dart'; // Import for Windows window management

class WebViewPage extends StatefulWidget {
  final Channel channel;
  final String channelUrl;
  const WebViewPage({
    super.key,
    required this.channel,
    required this.channelUrl,
  });

  @override
  State<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> {
  InAppWebViewController? _webViewController;
  late final TextEditingController _urlController;
  bool _shouldBlockAds = true;
  bool _isDisposing =
      false; // New flag to control widget rendering during disposal
  bool _isFullScreen = false; // New state for fullscreen mode

  // Enhanced ad blocking patterns including aggressive redirects and downloads
  final List<String> _adBlockPatterns = [
    // Common ad networks
    'doubleclick.net',
    'googleadservices.com',
    'googlesyndication.com',
    'googletagmanager.com',
    'google-analytics.com',
    'facebook.com/tr',
    'amazon-adsystem.com',
    'adsystem.amazon.com',
    'ads.yahoo.com',
    'advertising.com',
    'adsystem.com',
    'adsense.com',
    'adnxs.com',
    'ads.twitter.com',
    'analytics.twitter.com',
    'ads.linkedin.com',
    'ads.pinterest.com',
    'ads.reddit.com',
    'outbrain.com',
    'taboola.com',
    'scorecardresearch.com',
    'quantserve.com',

    // Aggressive redirect and download patterns
    'operagx.gg',
    'opera.com',
    'operagx.com',
    'download.opera.com',
    'get.opera.com',
    'install.opera.com',
    'setup.opera.com',
    'redirect.opera.com',
    'cdn.opera.com',
    'operacdn.com',
    'operasoftware.com',
    'opera-api.com',

    // Common redirect domains
    'bit.ly',
    'tinyurl.com',
    'short.link',
    'redirect.link',
    'go.link',
    'click.link',
    'track.link',
    'affiliate.link',
    'promo.link',
    'offer.link',

    // Download and installer patterns
    'download.',
    'installer.',
    'setup.',
    'install.',
    'get.',
    'fetch.',
    'grab.',
    'dl.',
    '.exe',
    '.msi',
    '.dmg',
    '.pkg',
    '.deb',
    '.rpm',

    // Generic ad patterns
    'adsystem',
    'advertising',
    'googleads',
    'adsense',
    'adservice',
    'adserver',
    'adnetwork',
    'adnxs',
    'ads.',
    '/ads/',
    'advertisement',
    'popup',
    'popunder',
    'interstitial',
    'overlay',
    'banner',
    'sponsored',
    'promo',
    'affiliate',
    'referral',
    'tracking',
    'analytics',
    'metrics',
    'telemetry',
  ];

  // Allowlist for video player and streaming domains
  final List<String> _allowedDomains = [
    'jwpcdn.com',
    'jwplatform.com',
    'jwplayer.com',
    'videojs.com',
    'vimeo.com',
    'youtube.com',
    'youtu.be',
    'dailymotion.com',
    'twitch.tv',
    'hls.js',
    'dash.js',
    'shaka-player',
    'plyr.io',
    'flowplayer.com',
    'brightcove.com',
    'kaltura.com',
    'wistia.com',
    'vidyard.com',
    'cloudflare.com',
    'amazonaws.com',
    'azure.com',
    'googleapis.com',
    'gstatic.com',
    'jsdelivr.net',
    'unpkg.com',
    'cdnjs.cloudflare.com',
    'profitableratecpm.com', // Whitelist Adsterra domain
  ];

  bool _shouldBlockUrl(String url) {
    if (!_shouldBlockAds) return false;

    final lowerUrl = url.toLowerCase();

    // Don't block allowed video player domains
    if (_allowedDomains.any((domain) => lowerUrl.contains(domain))) {
      return false;
    }

    // Check against our pattern list
    if (_adBlockPatterns.any((pattern) => lowerUrl.contains(pattern))) {
      return true;
    }

    // Additional checks for aggressive redirects
    if (_isAggressiveRedirect(lowerUrl)) {
      return true;
    }

    // Block download attempts
    if (_isDownloadAttempt(lowerUrl)) {
      return true;
    }

    return false;
  }

  bool _isAggressiveRedirect(String url) {
    // Block URLs that look like aggressive redirects
    final redirectPatterns = [
      RegExp(r'redirect\..*'),
      RegExp(r'.*\/redirect\/.*'),
      RegExp(r'.*\/go\/.*'),
      RegExp(r'.*\/click\/.*'),
      RegExp(r'.*\/track\/.*'),
      RegExp(r'.*\/out\/.*'),
      RegExp(r'.*\/exit\/.*'),
      RegExp(r'.*\/link\/.*'),
      RegExp(r'.*\/promo\/.*'),
      RegExp(r'.*\/offer\/.*'),
      RegExp(r'.*\/download\/.*'),
      RegExp(r'.*\/install\/.*'),
      RegExp(r'.*\/setup\/.*'),
      RegExp(r'.*\/get\/.*'),
    ];

    return redirectPatterns.any((pattern) => pattern.hasMatch(url));
  }

  bool _isDownloadAttempt(String url) {
    // Block direct download attempts
    final downloadExtensions = [
      '.exe',
      '.msi',
      '.dmg',
      '.pkg',
      '.deb',
      '.rpm',
      '.zip',
      '.rar',
      '.7z',
      '.tar.gz',
      '.tar.bz2',
      '.apk',
      '.ipa',
      '.app',
      '.bin',
      '.run',
    ];

    return downloadExtensions.any((ext) => url.endsWith(ext));
  }

  @override
  void initState() {
    super.initState();
    _urlController = TextEditingController(text: widget.channelUrl);
    // Store the original app bar height
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _urlController.dispose();
    _isDisposing = true; // Set flag to true to stop rendering the WebView
    // Ensure fullscreen mode is exited when the page is disposed
    if (_isFullScreen) {
      _toggleFullscreen();
    }

    // Improved disposal for Windows to prevent "used after disposed" error
    if (_webViewController != null) {
      // Schedule disposal after a longer delay to ensure native cleanup
      Future.delayed(const Duration(milliseconds: 100), () {
        try {
          _webViewController?.dispose();
        } catch (e) {
          // Ignore disposal errors as the widget is already being disposed
        } finally {
          _webViewController = null;
        }
      });
    }
    super.dispose();
  }

  void _toggleFullscreen() {
    if (kDebugMode) {
      print("Before fullscreen toggle: ${MediaQuery.of(context).size}");
    }

    setState(() {
      _isFullScreen = !_isFullScreen;
    });

    if (_isFullScreen) {
      SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.immersiveSticky,
        overlays: [],
      );
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
      if (defaultTargetPlatform == TargetPlatform.windows) {
        WindowManagerWindows.enterFullscreen();
      }
    } else {
      SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.edgeToEdge,
        overlays: SystemUiOverlay.values,
      );
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);
      if (defaultTargetPlatform == TargetPlatform.windows) {
        WindowManagerWindows.exitFullscreen();
      }
    }

    // Add a post-frame callback to get the size after the layout has updated
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (kDebugMode) {
        print("After fullscreen toggle: ${MediaQuery.of(context).size}");
      }
    });
  }

  void _loadUrl() {
    final url = _urlController.text.trim();
    if (url.isNotEmpty) {
      _webViewController?.loadUrl(urlRequest: URLRequest(url: WebUri(url)));
    }
  }

  Future<void> _injectAdBlockingScript(
    InAppWebViewController controller,
  ) async {
    const script = '''
      (function() {
        console.log('Ad blocking script injected');

        // Block aggressive redirects
        const originalOpen = window.open;
        window.open = function(url, name, specs) {
          console.log('Blocked window.open attempt:', url);
          return null;
        };

        // Block location changes
        const originalAssign = window.location.assign;
        const originalReplace = window.location.replace;
        const originalReload = window.location.reload;

        window.location.assign = function(url) {
          if (url && (url.includes('opera') || url.includes('download') || url.includes('install'))) {
            console.log('Blocked location.assign:', url);
            return;
          }
          return originalAssign.call(this, url);
        };

        window.location.replace = function(url) {
          if (url && (url.includes('opera') || url.includes('download') || url.includes('install'))) {
            console.log('Blocked location.replace:', url);
            return;
          }
          return originalReplace.call(this, url);
        };

        // Block document.location changes (only if not already defined)
        try {
          let originalLocation = document.location;
          Object.defineProperty(document, 'location', {
            get: function() { return originalLocation; },
            set: function(url) {
              if (url && (url.includes('opera') || url.includes('download') || url.includes('install'))) {
                console.log('Blocked document.location change:', url);
                return;
              }
              originalLocation = url;
            },
            configurable: true
          });
        } catch (e) {
          console.log('Could not redefine document.location (already defined):', e.message);
        }

        // Block aggressive click handlers (but preserve player controls)
        document.addEventListener('click', function(e) {
          const target = e.target;
          const href = target.href || target.getAttribute('href');

          // Check if this is a player control element
          const isPlayerControl = target.closest('video') ||
                                target.closest('[class*="player"]') ||
                                target.closest('[class*="video"]') ||
                                target.closest('[class*="control"]') ||
                                target.closest('[class*="quality"]') ||
                                target.closest('[class*="menu"]') ||
                                target.closest('[class*="button"]') ||
                                target.closest('[role="button"]') ||
                                target.hasAttribute('aria-label') ||
                                target.tagName === 'BUTTON';

          if (!isPlayerControl && href && (href.includes('opera') || href.includes('download') || href.includes('install'))) {
            console.log('Blocked click on suspicious link:', href);
            e.preventDefault();
            e.stopPropagation();
            return false;
          }
        }, true);

        // Block form submissions to suspicious URLs
        document.addEventListener('submit', function(e) {
          const action = e.target.action;
          if (action && (action.includes('opera') || action.includes('download') || action.includes('install'))) {
            console.log('Blocked form submission to:', action);
            e.preventDefault();
            e.stopPropagation();
            return false;
          }
        }, true);

        // Remove suspicious elements (but preserve video player controls)
        function removeSuspiciousElements() {
          const suspiciousSelectors = [
            'a[href*="opera"]:not([class*="player"]):not([class*="control"]):not([class*="video"])',
            'a[href*="download"]:not([class*="player"]):not([class*="control"]):not([class*="video"])',
            'a[href*="install"]:not([class*="player"]):not([class*="control"]):not([class*="video"])',
            'iframe[src*="opera"]:not([class*="player"]):not([class*="video"])',
            'iframe[src*="download"]:not([class*="player"]):not([class*="video"])',
            'iframe[src*="install"]:not([class*="player"]):not([class*="video"])',
            '.popup:not([class*="player"]):not([class*="control"]):not([class*="video"]):not([class*="quality"]):not([class*="menu"])',
            '.interstitial:not([class*="player"]):not([class*="control"]):not([class*="video"])',
            '[id*="popup"]:not([id*="player"]):not([id*="control"]):not([id*="video"]):not([id*="quality"]):not([id*="menu"])',
            '[class*="popup"]:not([class*="player"]):not([class*="control"]):not([class*="video"]):not([class*="quality"]):not([class*="menu"])'
          ];

          suspiciousSelectors.forEach(selector => {
            try {
              const elements = document.querySelectorAll(selector);
              elements.forEach(el => {
                // Additional check to avoid removing video player elements
                const isPlayerElement = el.closest('video') ||
                                      el.closest('[class*="player"]') ||
                                      el.closest('[class*="video"]') ||
                                      el.closest('[class*="control"]') ||
                                      el.closest('[class*="quality"]') ||
                                      el.closest('[class*="menu"]') ||
                                      el.querySelector('video') ||
                                      el.querySelector('[class*="player"]') ||
                                      el.querySelector('[class*="video"]');

                if (!isPlayerElement) {
                  console.log('Removing suspicious element:', el);
                  el.remove();
                }
              });
            } catch (e) {
              // Ignore errors
            }
          });
        }

        // Run immediately and on DOM changes
        removeSuspiciousElements();

        // Watch for new elements
        const observer = new MutationObserver(function(mutations) {
          mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
              removeSuspiciousElements();
            }
          });
        });

        observer.observe(document.body, {
          childList: true,
          subtree: true
        });

        console.log('Ad blocking script fully loaded');
      })();
    ''';

    try {
      await controller.evaluateJavascript(source: script);
      if (kDebugMode) {
        print('Ad blocking script injected successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error injecting ad blocking script: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return KeyboardListener(
      focusNode: FocusNode(),
      autofocus: true,
      onKeyEvent: (KeyEvent event) {
        if (event is KeyDownEvent &&
            event.logicalKey == LogicalKeyboardKey.escape) {
          if (_isFullScreen) {
            _toggleFullscreen();
          }
        }
      },
      child: PopScope(
        canPop: !_isFullScreen, // Allow pop if not fullscreen
        onPopInvokedWithResult: (didPop, result) {
          if (_isFullScreen) {
            _toggleFullscreen();
          }
        },
        child: Scaffold(
          extendBodyBehindAppBar: true,
          backgroundColor: Colors.black,
          appBar:
              _isFullScreen
                  ? null // Hide AppBar in fullscreen
                  : AppBar(
                    title: Text(
                      widget.channel.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 20,
                      ),
                    ),
                    backgroundColor: Colors.transparent,
                    elevation: 0,
                    centerTitle: false, // Changed to false to align title left
                    flexibleSpace: ClipRRect(
                      child: BackdropFilter(
                        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                        child: Container(
                          decoration: const BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                Color.fromARGB(255, 50, 0, 50), // Dark purple
                                Color.fromARGB(255, 20, 20, 20), // Dark grey
                              ],
                            ),
                            border: Border(
                              bottom: BorderSide(
                                color: Colors.white10,
                                width: 0.5,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    leading: IconButton(
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      tooltip: 'Back to Channels',
                    ),
                    actions: [
                      IconButton(
                        icon: Icon(
                          _shouldBlockAds
                              ? Icons.shield
                              : Icons.shield_outlined,
                          color: Colors.white,
                          size:
                              28.0, // Making it slightly larger for "thick" effect
                        ),
                        onPressed: () {
                          setState(() {
                            _shouldBlockAds = !_shouldBlockAds;
                          });
                          _loadUrl();
                        },
                        tooltip:
                            _shouldBlockAds
                                ? 'Disable Ad Blocking'
                                : 'Enable Ad Blocking',
                      ),
                      IconButton(
                        icon: Icon(
                          _isFullScreen
                              ? Icons.fullscreen_exit
                              : Icons.fullscreen,
                          color: Colors.white,
                          size:
                              28.0, // Making it slightly larger for "thick" effect
                        ),
                        onPressed: _toggleFullscreen,
                        tooltip:
                            _isFullScreen ? 'Exit Fullscreen' : 'Fullscreen',
                      ),
                      IconButton(
                        icon: const Icon(
                          Icons.refresh,
                          color: Colors.white,
                          size:
                              28.0, // Making it slightly larger for "thick" effect
                        ),
                        onPressed: _loadUrl,
                        tooltip: 'Refresh', // Added tooltip
                      ),
                    ],
                  ),
          body: Stack(
            children: [
              Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color.fromARGB(255, 20, 20, 20),
                      Color.fromARGB(255, 50, 0, 50),
                    ],
                  ),
                ),
                child:
                    _isDisposing
                        ? const SizedBox.shrink()
                        : SafeArea(
                          child: Center(
                            child: InAppWebView(
                              initialUrlRequest: URLRequest(
                                url: WebUri(widget.channelUrl),
                              ),
                              initialSettings: InAppWebViewSettings(
                                javaScriptEnabled: true,
                                mediaPlaybackRequiresUserGesture: false,
                                domStorageEnabled: true,
                                databaseEnabled: true,
                                javaScriptCanOpenWindowsAutomatically: false,
                                supportMultipleWindows: false,
                                allowsInlineMediaPlayback: true,
                                allowsBackForwardNavigationGestures: false,
                                allowsAirPlayForMediaPlayback: false,
                                allowFileAccessFromFileURLs: false,
                                allowUniversalAccessFromFileURLs: false,
                                mixedContentMode:
                                    MixedContentMode.MIXED_CONTENT_NEVER_ALLOW,
                              ),
                              shouldOverrideUrlLoading: (
                                controller,
                                navigationAction,
                              ) async {
                                final url =
                                    navigationAction.request.url.toString();
                                if (_shouldBlockUrl(url)) {
                                  if (kDebugMode) {
                                    debugPrint("Blocking ad request: $url");
                                  }
                                  return NavigationActionPolicy.CANCEL;
                                }
                                return NavigationActionPolicy.ALLOW;
                              },
                              onCreateWindow: (
                                controller,
                                createWindowRequest,
                              ) async {
                                if (kDebugMode) {
                                  print(
                                    "Blocking popup: ${createWindowRequest.request.url}",
                                  );
                                }
                                return false;
                              },
                              onWebViewCreated: (controller) {
                                _webViewController = controller;
                              },
                              onLoadStart: (controller, url) {
                                if (kDebugMode) {
                                  print("WebView started loading: $url");
                                }
                              },
                              onLoadStop: (controller, url) async {
                                if (kDebugMode) {
                                  print("WebView finished loading: $url");
                                }
                                await _injectAdBlockingScript(controller);
                              },
                              onReceivedError: (controller, request, error) {
                                if (kDebugMode) {
                                  print(
                                    "Error loading ${request.url}: ${error.description} (Code: ${error.type})",
                                  );
                                }
                              },
                              onConsoleMessage: (controller, consoleMessage) {
                                if (kDebugMode) {
                                  print(
                                    "Console Message: ${consoleMessage.message}",
                                  );
                                }
                              },
                            ),
                          ),
                        ),
              ),
              // Escape button for fullscreen mode
              Offstage(
                offstage: !_isFullScreen,
                child: Align(
                  alignment: Alignment.topLeft,
                  child: Padding(
                    padding: EdgeInsets.only(
                      top: MediaQuery.of(context).padding.top + 10,
                      left: 10,
                    ),
                    child: FloatingActionButton(
                      mini: true,
                      backgroundColor: Colors.black54,
                      onPressed: _toggleFullscreen,
                      child: const Icon(
                        Icons.fullscreen_exit,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
