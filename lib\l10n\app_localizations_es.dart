// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Spanish Castilian (`es`).
class AppLocalizationsEs extends AppLocalizations {
  AppLocalizationsEs([String locale = 'es']) : super(locale);

  @override
  String get appTitle => 'TV de Gatos';

  @override
  String get helloWorld => 'Hola Mundo';

  @override
  String welcomeMessage(Object userName) {
    return '¡Bienvenido, $userName!';
  }

  @override
  String get noSourcesAvailable => 'No hay fuentes disponibles para este canal';

  @override
  String get liveTab => 'En vivo';

  @override
  String get fixturesTab => 'Partidos';

  @override
  String get favoritesTab => 'Favoritos';

  @override
  String get channelsTitle => 'Canales';

  @override
  String get channelListPlaceholder => 'La lista de canales estará aquí';

  @override
  String get language_ar => 'Árabe';

  @override
  String get language_en => 'Inglés';

  @override
  String get language_es => 'Español';

  @override
  String get language_fr => 'Francés';

  @override
  String get language_pl => 'Polaco';

  @override
  String get disclaimerTitle => 'Descargo de responsabilidad';

  @override
  String get disclaimerText => 'Esta aplicación no aloja, almacena ni controla ninguna transmisión de video o logotipos mostrados. Todas las transmisiones se obtienen externamente de enlaces IPTV disponibles públicamente mantenidos por el proyecto IPTV-org. La aplicación es solo para uso personal e informativo. Todos los logotipos y el contenido del canal son propiedad de sus respectivos dueños.';

  @override
  String appVersion(Object version) {
    return 'Versión de la aplicación: $version';
  }

  @override
  String get settingsTitle => 'Configuración';

  @override
  String get appLanguage => 'Idioma de la aplicación';

  @override
  String get adsEnabled => 'Mostrar anuncios';

  @override
  String get enabled => 'Habilitado';

  @override
  String get disabled => 'Deshabilitado';

  @override
  String get updatingServersData => 'Actualizando datos de los servidores...';

  @override
  String get serversUpdateSuccess => '¡Datos de los servidores actualizados con éxito!';

  @override
  String get failedToUpdateServers => 'Fallo al actualizar los datos de los servidores.';

  @override
  String get serversUpdateCancelled => 'Actualización de datos de los servidores cancelada.';

  @override
  String nextServerUpdate(Object date) {
    return 'Próxima actualización del servidor: $date';
  }

  @override
  String get alreadyUpToDate => '¡Ya está actualizado!';

  @override
  String get serversUpToDate => 'Servers data is up to date.';

  @override
  String iptvDataLastUpdate(Object date) {
    return 'IPTV Data Last Update: $date';
  }
}
