import 'package:flutter/material.dart';

class PlayerTopBar extends StatelessWidget {
  final bool hasSource;
  final VoidCallback? onShowCatSoundEffect;
  final VoidCallback? onStartChromecast;

  const PlayerTopBar({
    super.key,
    required this.hasSource,
    this.onShowCatSoundEffect,
    this.onStartChromecast,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Top left logo
        Positioned(
          top: 16,
          left: 16,
          child: GestureDetector(
            onTap: () => onShowCatSoundEffect?.call(),
            child: SizedBox(
              width: 100,
              height: 100,
              child: Image.asset('assets/logo/logo.png'),
            ),
          ),
        ),
        // Chromecast button (top right corner)
        if (hasSource)
          Positioned(
            top: 16,
            right: 16,
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.transparent, // No background
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: Colors.white, // White outline
                  width: 1.5, // Outline thickness
                ),
              ),
              child: IconButton(
                icon: const Icon(Icons.cast, color: Colors.white, size: 20),
                onPressed: () => onStartChromecast?.call(),
              ),
            ),
          ),
      ],
    );
  }
}
