import 'package:flutter/material.dart';
import 'dart:ui'; // Required for ImageFilter
import 'package:cat_tv/models/fixture.dart';

class FixturesListPage extends StatefulWidget {
  const FixturesListPage({super.key});

  @override
  State<FixturesListPage> createState() => _FixturesListPageState();
}

class _FixturesListPageState extends State<FixturesListPage> {
  List<FixtureLeague> _leagues = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadFixtures();
  }

  Future<void> _loadFixtures() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    // Since scraping is removed, we'll just return an empty list for now.
    // In a real application, this would be replaced with a new data source.
    await Future.delayed(
      const Duration(milliseconds: 500),
    ); // Simulate network delay
    if (!mounted) return; // Check if the widget is still mounted
    setState(() {
      _leagues = [];
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    Widget content;

    if (_isLoading) {
      content = const Center(child: CircularProgressIndicator());
    } else if (_errorMessage != null) {
      content = Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.white70),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: const TextStyle(fontSize: 16, color: Colors.white70),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadFixtures,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.deepPurpleAccent,
                foregroundColor: Colors.white,
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    } else if (_leagues.isEmpty) {
      content = const Center(
        child: Text(
          'No fixtures available',
          style: TextStyle(fontSize: 16, color: Colors.white70),
        ),
      );
    } else {
      content = RefreshIndicator(
        onRefresh: _loadFixtures,
        child: ListView.builder(
          itemCount: _leagues.length,
          itemBuilder: (context, index) {
            final league = _leagues[index];
            return _buildLeagueCard(league);
          },
        ),
      );
    }

    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color.fromARGB(255, 20, 20, 20),
            Color.fromARGB(255, 50, 0, 50),
          ],
        ),
      ),
      child: content,
    );
  }

  Widget _buildLeagueCard(FixtureLeague league) {
    if (league.matches.isEmpty) {
      return const SizedBox.shrink(); // Don't show leagues with no matches
    }

    final double borderRadius = 12.0;

    return Card(
      elevation: 4.0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      color: Colors.transparent,
      margin: const EdgeInsets.all(8.0),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
          child: Container(
            decoration: BoxDecoration(
              color: const Color.fromARGB(255, 40, 40, 40),
              borderRadius: BorderRadius.circular(borderRadius),
              border: Border.all(
                color: const Color.fromARGB(255, 60, 60, 60),
                width: 1.0,
              ),
            ),
            child: ExpansionTile(
              leading: Image.network(
                league.flagUrl,
                width: 32,
                height: 24,
                errorBuilder: (context, error, stackTrace) {
                  return Icon(Icons.flag, size: 24, color: Colors.grey[400]);
                },
              ),
              title: Text(
                league.leagueName,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: Colors.white,
                ),
              ),
              subtitle: Text(
                '${league.matches.length} matches',
                style: const TextStyle(color: Colors.white70),
              ),
              children:
                  league.matches
                      .map((match) => _buildMatchCard(match))
                      .toList(),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMatchCard(FixtureMatch match) {
    final parsed = match.parsedTeams;
    final status = match.status;
    final double padding = 8.0;
    final double borderRadius = 8.0; // Slightly smaller for inner cards

    // Debug prints for flag URLs
    debugPrint(
      'Home Team: ${parsed['homeTeam']}, Flag URL: ${match.homeTeamFlagUrl}',
    );
    debugPrint(
      'Away Team: ${parsed['awayTeam']}, Flag URL: ${match.awayTeamFlagUrl}',
    );

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      child: Card(
        elevation: 2.0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        color: Colors.transparent,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(borderRadius),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0), // Less blur
            child: Container(
              decoration: BoxDecoration(
                color: const Color.fromARGB(255, 50, 50, 50),
                borderRadius: BorderRadius.circular(borderRadius),
                border: Border.all(
                  color: const Color.fromARGB(255, 70, 70, 70),
                  width: 0.5,
                ),
              ),
              child: InkWell(
                borderRadius: BorderRadius.circular(borderRadius),
                onTap: () {
                  _showMatchDetails(match);
                },
                child: Padding(
                  padding: EdgeInsets.all(padding),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          _buildTeamWithLogo(
                            parsed['homeTeam'],
                            match.homeTeamFlagUrl,
                            true,
                          ),
                          const SizedBox(width: 8),
                          _buildScoreWidget(match),
                          const SizedBox(width: 8),
                          _buildTeamWithLogo(
                            parsed['awayTeam'],
                            match.awayTeamFlagUrl,
                            false,
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          _buildStatusChip(status),
                          const SizedBox(width: 8),
                          Text(
                            match.time,
                            style: TextStyle(
                              color: Colors.white70,
                              fontSize: 12,
                            ),
                          ),
                          if (status == MatchStatus.upcoming &&
                              match.timeUntilStart != null)
                            Text(
                              ' • ${_formatTimeUntilStart(match.timeUntilStart!)}',
                              style: TextStyle(
                                color: Colors.white70,
                                fontSize: 12,
                              ),
                            ),
                        ],
                      ),
                      if (match.channels.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Wrap(
                          spacing: 4,
                          runSpacing: 4,
                          alignment: WrapAlignment.center,
                          children:
                              match.channels.take(3).map((channel) {
                                return Chip(
                                  label: Text(
                                    channel,
                                    style: const TextStyle(
                                      fontSize: 10,
                                      color: Colors.white,
                                    ),
                                  ),
                                  backgroundColor: const Color.fromARGB(
                                    255,
                                    90,
                                    0,
                                    90,
                                  ),
                                  materialTapTargetSize:
                                      MaterialTapTargetSize.shrinkWrap,
                                  visualDensity: VisualDensity.compact,
                                );
                              }).toList(),
                        ),
                        if (match.channels.length > 3)
                          Text(
                            '+${match.channels.length - 3} more channels',
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.white70,
                            ),
                          ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTeamWithLogo(String teamName, String? flagUrl, bool isHome) {
    return Expanded(
      child: Row(
        mainAxisAlignment:
            isHome ? MainAxisAlignment.start : MainAxisAlignment.end,
        children: [
          if (isHome) ...[_buildTeamFlag(flagUrl), const SizedBox(width: 8)],
          Flexible(
            child: Text(
              teamName,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
              overflow: TextOverflow.ellipsis,
              textAlign: isHome ? TextAlign.left : TextAlign.right,
            ),
          ),
          if (!isHome) ...[const SizedBox(width: 8), _buildTeamFlag(flagUrl)],
        ],
      ),
    );
  }

  Widget _buildTeamFlag(String? flagUrl) {
    if (flagUrl == null || flagUrl.isEmpty) {
      return Container(
        width: 24,
        height: 24,
        decoration: BoxDecoration(
          // shape: BoxShape.circle, // Removed to default to rectangle
        ),
        child: ClipRRect(
          // Changed from ClipOval
          borderRadius: BorderRadius.circular(0), // For square corners
          child: Container(
            color: const Color.fromARGB(255, 60, 60, 60),
            child: const Icon(
              Icons.sports_soccer,
              size: 16,
              color: Colors.white70,
            ),
          ),
        ),
      );
    }
    return Container(
      width: 24,
      height: 24,
      decoration: BoxDecoration(
        // shape: BoxShape.circle, // Removed to default to rectangle
      ),
      child: ClipRRect(
        // Changed from ClipOval
        borderRadius: BorderRadius.circular(0), // For square corners
        child: Image.network(
          flagUrl,
          width: 24,
          height: 24,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              color: const Color.fromARGB(255, 60, 60, 60),
              child: const Icon(
                Icons.sports_soccer,
                size: 16,
                color: Colors.white70,
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildScoreWidget(FixtureMatch match) {
    final parsed = match.parsedTeams;
    final status = match.status;

    if (status == MatchStatus.finished) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        decoration: BoxDecoration(
          color: const Color.fromARGB(255, 70, 70, 70),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          '${parsed['homeScore']} - ${parsed['awayScore']}',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );
    } else {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        decoration: BoxDecoration(
          color: const Color.fromARGB(255, 70, 70, 70),
          borderRadius: BorderRadius.circular(4),
        ),
        child: const Text(
          'vs',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
        ),
      );
    }
  }

  Widget _buildStatusChip(MatchStatus status) {
    Color color;
    String text;

    switch (status) {
      case MatchStatus.live:
        color = Colors.red;
        text = 'LIVE';
        break;
      case MatchStatus.finished:
        color = Colors.grey;
        text = 'FT';
        break;
      case MatchStatus.upcoming:
        color = Colors.blue;
        text = 'UPCOMING';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(3),
      ),
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  String _formatTimeUntilStart(Duration duration) {
    if (duration.inDays > 0) {
      return '${duration.inDays}d ${duration.inHours % 24}h';
    } else if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes % 60}m';
    } else {
      return '${duration.inMinutes}m';
    }
  }

  void _showMatchDetails(FixtureMatch match) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent, // Make background transparent
      builder: (context) {
        return ClipRRect(
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.black.withValues(
                  alpha: 0.4 * 255,
                ), // Darker background
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(20),
                ),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.2 * 255),
                  width: 1.0,
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    match.title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white, // White text
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Time: ${match.time}',
                    style: const TextStyle(color: Colors.white70), // White text
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Broadcasting Channels:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white, // White text
                    ),
                  ),
                  const SizedBox(height: 4),
                  ...match.channels.map(
                    (channel) => Padding(
                      padding: const EdgeInsets.only(left: 8, top: 2),
                      child: Text(
                        '• $channel',
                        style: const TextStyle(
                          color: Colors.white70,
                        ), // White text
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
