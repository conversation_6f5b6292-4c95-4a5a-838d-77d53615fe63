import 'package:flutter/material.dart';
import 'dart:ui'; // Required for ImageFilter
import 'package:cat_tv/db/database_loader.dart';
import 'package:cat_tv/repositories/channel_repository.dart';
import 'package:cat_tv/models/channel.dart';
import 'package:cat_tv/services/favorites_service.dart';
import 'package:sqflite/sqflite.dart';
import 'package:flutter/foundation.dart';
import 'package:cat_tv/pages/player_page.dart';
import 'package:cat_tv/pages/webview_page.dart';
import 'package:country_flags/country_flags.dart'; // Import for country flags
import 'package:cat_tv/widgets/channel_skeleton_loader.dart'; // Import the skeleton loader
import 'package:cat_tv/utils/display_mode.dart'; // Import the common DisplayMode enum

class FavoritesPage extends StatefulWidget {
  const FavoritesPage({super.key});

  @override
  State<FavoritesPage> createState() => _FavoritesPageState();
}

class _FavoritesPageState extends State<FavoritesPage> {
  List<Channel> _channels = [];
  bool _isLoading = false;
  String? _error;
  late ChannelRepository _repo;
  late Database _db;
  DisplayMode _displayMode = DisplayMode.list; // Default display mode

  @override
  void initState() {
    super.initState();
    _initDbAndLoad();
  }

  Future<void> _initDbAndLoad() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Ensure FavoritesService is initialized
      await FavoritesService.init();

      _db = await DatabaseLoader.openPrebuiltDatabase();
      _repo = ChannelRepository(_db);
      await _loadFavoriteChannels();
    } catch (e) {
      setState(() {
        _error = 'Failed to initialize: $e';
        _isLoading = false;
      });
      if (kDebugMode) {
        print('Error in _initDbAndLoad: $e');
      }
    }
  }

  Future<void> _loadFavoriteChannels() async {
    try {
      // Use the optimized method that doesn't create infinite loops
      final favoriteChannels = await _repo.getFavoriteChannels();
      if (kDebugMode) {
        print('Loaded ${favoriteChannels.length} favorite channels');
      }

      setState(() {
        _channels = favoriteChannels;
        _isLoading = false;
      });
    } catch (e, st) {
      if (kDebugMode) {
        print('Error loading favorite channels: $e');
        print(st);
      }
      setState(() {
        _error = 'Failed to load favorites: $e';
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  Widget _buildChannelList() {
    if (_isLoading && _channels.isEmpty) {
      return ChannelSkeletonLoader(displayMode: _displayMode);
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 64),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _initDbAndLoad,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.deepPurpleAccent,
                foregroundColor: Colors.white,
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_channels.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.favorite_border, size: 64, color: Colors.white70),
            SizedBox(height: 16),
            Text(
              'No favorite channels yet.',
              style: TextStyle(fontSize: 18, color: Colors.white70),
            ),
            SizedBox(height: 8),
            Text(
              'Add channels to favorites from the main page!',
              style: TextStyle(color: Colors.white70),
            ),
          ],
        ),
      );
    }

    Widget currentView;
    if (_displayMode == DisplayMode.list) {
      currentView = RefreshIndicator(
        key: const ValueKey('favoriteListView'), // Key for AnimatedSwitcher
        onRefresh: _loadFavoriteChannels,
        child: ListView.builder(
          padding: const EdgeInsets.all(8.0),
          itemCount: _channels.length,
          itemBuilder: (context, index) {
            final channel = _channels[index];
            return _buildChannelCard(context, channel, _displayMode);
          },
        ),
      );
    } else {
      currentView = RefreshIndicator(
        key: const ValueKey('favoriteGridView'), // Key for AnimatedSwitcher
        onRefresh: _loadFavoriteChannels,
        child: GridView.builder(
          padding: const EdgeInsets.all(8.0),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 5, // Always 5 for small tiles
            crossAxisSpacing: 8.0,
            mainAxisSpacing: 8.0,
            childAspectRatio: 1.0, // Always 1.0 for square small tiles
          ),
          itemCount: _channels.length,
          itemBuilder: (context, index) {
            final channel = _channels[index];
            return _buildChannelCard(context, channel, _displayMode);
          },
        ),
      );
    }

    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 150), // Reduced duration
      transitionBuilder: (Widget child, Animation<double> animation) {
        return FadeTransition(
          opacity: animation,
          child: ScaleTransition(scale: animation, child: child),
        );
      },
      child: currentView,
    );
  }

  Widget _buildChannelCard(
    BuildContext context,
    Channel channel,
    DisplayMode mode,
  ) {
    final bool isList = mode == DisplayMode.list;
    final bool isSmallTiles = mode == DisplayMode.smallTiles;
    final double imageSize = isList ? 40 : 40;
    final double fontSize = isList ? 14 : 10;
    final double favoriteIconSize = isList ? 24 : 20;
    final double padding = isList ? 8.0 : 4.0;
    final double borderRadius = isList ? 12.0 : 8.0;

    return Card(
      elevation: 4.0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      color: Colors.transparent,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: .1),
              borderRadius: BorderRadius.circular(borderRadius),
              border: Border.all(
                color: Colors.white.withValues(alpha: .2),
                width: 1.0,
              ),
            ),
            child: InkWell(
              borderRadius: BorderRadius.circular(borderRadius),
              onTap: () async {
                await _handleChannelTap(context, channel);
              },
              child: Padding(
                padding: EdgeInsets.all(padding),
                child:
                    isList
                        ? Row(
                          children: [
                            channel.logoUrl != null &&
                                    channel.logoUrl!.isNotEmpty
                                ? ClipRRect(
                                  borderRadius: BorderRadius.circular(8.0),
                                  child: Image.network(
                                    channel.logoUrl!,
                                    width: imageSize,
                                    height: imageSize,
                                    fit: BoxFit.contain,
                                    errorBuilder:
                                        (context, error, stackTrace) => Icon(
                                          Icons.image_not_supported,
                                          size: imageSize,
                                        ),
                                  ),
                                )
                                : Icon(
                                  Icons.tv,
                                  size: imageSize,
                                  color: Colors.grey,
                                ),
                            SizedBox(width: 8.0),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    channel.name,
                                    style: TextStyle(
                                      fontSize: fontSize,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  Row(
                                    children: [
                                      CountryFlag.fromCountryCode(
                                        channel.countryCode,
                                        height: fontSize * 1.2,
                                        width: fontSize * 1.6,
                                        borderRadius: 4,
                                      ),
                                      SizedBox(width: 4),
                                      Text(
                                        channel.countryCode,
                                        style: TextStyle(
                                          fontSize: fontSize * 0.8,
                                          color: Colors.white70,
                                        ),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      SizedBox(width: 8),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            IconButton(
                              icon: Icon(
                                channel.isFavorite
                                    ? Icons.favorite
                                    : Icons.favorite_border,
                                color:
                                    channel.isFavorite
                                        ? Theme.of(context).colorScheme.primary
                                        : Colors.white70,
                                size: favoriteIconSize,
                              ),
                              onPressed: () async {
                                await channel.toggleFavorite();
                                _loadFavoriteChannels(); // Reload to reflect changes
                              },
                            ),
                          ],
                        )
                        : Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            channel.logoUrl != null &&
                                    channel.logoUrl!.isNotEmpty
                                ? ClipRRect(
                                  borderRadius: BorderRadius.circular(8.0),
                                  child: Image.network(
                                    channel.logoUrl!,
                                    width: imageSize,
                                    height: imageSize,
                                    fit: BoxFit.contain,
                                    errorBuilder:
                                        (context, error, stackTrace) => Icon(
                                          Icons.image_not_supported,
                                          size: imageSize,
                                        ),
                                  ),
                                )
                                : Icon(
                                  Icons.tv,
                                  size: imageSize,
                                  color: Colors.grey,
                                ),
                            SizedBox(height: isSmallTiles ? 4.0 : 8.0),
                            Text(
                              channel.name,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: fontSize,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            if (isList) ...[
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  CountryFlag.fromCountryCode(
                                    channel.countryCode,
                                    height: fontSize * 1.2,
                                    width: fontSize * 1.6,
                                    borderRadius: 4,
                                  ),
                                  SizedBox(width: 8),
                                  Text(
                                    channel.countryCode,
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      fontSize: fontSize * 0.8,
                                      color: Colors.white70,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ),
                              SizedBox(height: 4),
                            ],
                            IconButton(
                              icon: Icon(
                                channel.isFavorite
                                    ? Icons.favorite
                                    : Icons.favorite_border,
                                color:
                                    channel.isFavorite
                                        ? Theme.of(context).colorScheme.primary
                                        : Colors.white70,
                                size: favoriteIconSize,
                              ),
                              onPressed: () async {
                                await channel.toggleFavorite();
                                _loadFavoriteChannels(); // Reload to reflect changes
                              },
                            ),
                          ],
                        ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _handleChannelTap(BuildContext context, Channel channel) async {
    // Revert the change: tapping the tile should always navigate,
    // the favorite toggle will be handled by a dedicated icon.

    if (kDebugMode) {
      print('Fetching sources for channel: ${channel.channelId}');
    }
    final sources = await _repo.getChannelSources(channel.channelId);
    if (kDebugMode) {
      print('Channel sources for ${channel.channelId}: $sources');
    }
    if (!mounted) return;
    if (sources.isNotEmpty) {
      final isExternal = await _repo.isChannelSourceExternal(channel.channelId);
      if (!mounted) return;
      if (isExternal) {
        Navigator.push(
          // ignore: use_build_context_synchronously
          context,
          PageRouteBuilder(
            pageBuilder:
                (context, animation, secondaryAnimation) => WebViewPage(
                  channel: channel,
                  channelUrl: sources.first['source_url'],
                ),
            transitionsBuilder: (
              context,
              animation,
              secondaryAnimation,
              child,
            ) {
              return FadeTransition(opacity: animation, child: child);
            },
          ),
        );
      } else {
        Navigator.push(
          // ignore: use_build_context_synchronously
          context,
          PageRouteBuilder(
            pageBuilder:
                (context, animation, secondaryAnimation) =>
                    PlayerPage(channel: channel, channelSources: sources),
            transitionsBuilder: (
              context,
              animation,
              secondaryAnimation,
              child,
            ) {
              return FadeTransition(opacity: animation, child: child);
            },
          ),
        );
      }
    } else {
      // ignore: use_build_context_synchronously
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No sources available for this channel')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color.fromARGB(255, 20, 20, 20),
            Color.fromARGB(255, 50, 0, 50),
          ],
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 8.0,
                vertical: 4.0,
              ),
              child: SegmentedButton<DisplayMode>(
                segments: const <ButtonSegment<DisplayMode>>[
                  ButtonSegment<DisplayMode>(
                    value: DisplayMode.list,
                    icon: Icon(Icons.list_rounded),
                  ),
                  ButtonSegment<DisplayMode>(
                    value: DisplayMode.smallTiles,
                    icon: Icon(Icons.apps_rounded),
                  ),
                ],
                selected: <DisplayMode>{_displayMode},
                onSelectionChanged: (Set<DisplayMode> newSelection) {
                  setState(() {
                    _displayMode = newSelection.first;
                  });
                },
                style: SegmentedButton.styleFrom(
                  foregroundColor: Colors.white,
                  selectedForegroundColor: Colors.deepPurpleAccent,
                  selectedBackgroundColor: Colors.white.withValues(alpha: .2),
                  side: const BorderSide(color: Colors.white54),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
              ),
            ),
            Expanded(child: _buildChannelList()),
          ],
        ),
      ),
    );
  }
}
