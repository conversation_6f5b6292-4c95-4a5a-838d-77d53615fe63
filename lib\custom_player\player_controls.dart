import 'package:flutter/material.dart';
import 'package:flutter_hls_parser/flutter_hls_parser.dart'; // For Variant type

// Define callbacks for player actions
typedef OnSeekCallback = void Function(Duration duration);
typedef OnSetVolumeCallback = void Function(double volume);
typedef OnSetRateCallback = void Function(double rate);
typedef OnSwitchQualityCallback = void Function(Variant variant);
typedef OnShowMenuCallback = void Function(BuildContext context);
typedef OnToggleVolumeSliderVisibilityCallback = void Function(bool visible);

class PlayerControls extends StatelessWidget {
  final bool hasSource;
  final bool areControlsVisible;
  final bool isLiveStream;
  final Animation<double> liveAnimation;

  // Player state data
  final Duration currentPosition;
  final Duration totalDuration;
  final double currentVolume;
  final bool isPlaying;
  final double playbackSpeed;
  final String? currentQuality;
  final String? currentSubtitle;
  final bool isVolumeSliderVisible;
  final bool isFullScreenMode;

  // Callbacks for user interactions
  final VoidCallback? onPlayButtonPressed;
  final OnSeekCallback? onSeek;
  final OnSetVolumeCallback? onSetVolume;
  final VoidCallback? onToggleMute;
  final OnSetRateCallback? onSetRate;
  final OnSwitchQualityCallback? onSwitchQuality;
  final OnShowMenuCallback? onShowSubtitlesMenu;
  final OnShowMenuCallback? onShowSettingsMenu;
  final VoidCallback? onEnterPictureInPicture;
  final VoidCallback? onToggleFullScreen;
  final OnToggleVolumeSliderVisibilityCallback? onToggleVolumeSliderVisibility;

  const PlayerControls({
    super.key,
    required this.hasSource,
    required this.areControlsVisible,
    required this.isLiveStream,
    required this.liveAnimation,
    required this.currentPosition,
    required this.totalDuration,
    required this.currentVolume,
    required this.isPlaying,
    required this.playbackSpeed,
    this.currentQuality,
    this.currentSubtitle,
    required this.isVolumeSliderVisible,
    required this.isFullScreenMode,
    this.onPlayButtonPressed,
    this.onSeek,
    this.onSetVolume,
    this.onToggleMute,
    this.onSetRate,
    this.onSwitchQuality,
    this.onShowSubtitlesMenu,
    this.onShowSettingsMenu,
    this.onEnterPictureInPicture,
    this.onToggleFullScreen,
    this.onToggleVolumeSliderVisibility,
  });

  @override
  Widget build(BuildContext context) {
    if (!hasSource) {
      return Container(); // Don't show controls if no source
    }

    return AnimatedOpacity(
      opacity: areControlsVisible ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 300),
      child: IgnorePointer(
        ignoring: !areControlsVisible,
        child: Align(
          alignment: Alignment.bottomCenter,
          child: LayoutBuilder(
            builder: (context, constraints) {
              final isMobile =
                  constraints.maxWidth < 600; // Define a breakpoint for mobile
              return Container(
                color: Colors.transparent,
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    // Progress bar row
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      child: Row(
                        children: [
                          Text(
                            currentPosition.toString().split('.').first,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(width: 8),
                          if (totalDuration.inMilliseconds > 0)
                            Expanded(
                              child: SliderTheme(
                                data: SliderTheme.of(context).copyWith(
                                  trackHeight: 3.0,
                                  thumbShape: const RoundSliderThumbShape(
                                    enabledThumbRadius: 6.0,
                                  ),
                                  overlayShape: const RoundSliderOverlayShape(
                                    overlayRadius: 12.0,
                                  ),
                                ),
                                child: Slider(
                                  value:
                                      currentPosition.inMilliseconds.toDouble(),
                                  min: 0.0,
                                  max: totalDuration.inMilliseconds.toDouble(),
                                  onChanged: (value) {
                                    onSeek?.call(
                                      Duration(milliseconds: value.toInt()),
                                    );
                                  },
                                  activeColor: Colors.green,
                                  inactiveColor: Colors.grey,
                                ),
                              ),
                            ),
                          const SizedBox(width: 8),
                          Text(
                            totalDuration.toString().split('.').first,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (isMobile)
                      Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              // Left side: Volume control and Live indicator
                              Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  // Volume control
                                  MouseRegion(
                                    onEnter:
                                        (_) => onToggleVolumeSliderVisibility
                                            ?.call(true),
                                    onExit:
                                        (_) => onToggleVolumeSliderVisibility
                                            ?.call(false),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        IconButton(
                                          icon: Icon(
                                            currentVolume == 0
                                                ? Icons.volume_off
                                                : Icons.volume_up,
                                            color: Colors.white,
                                            size: 20.0,
                                          ),
                                          onPressed: () => onToggleMute?.call(),
                                        ),
                                        AnimatedOpacity(
                                          opacity:
                                              isVolumeSliderVisible ? 1.0 : 0.0,
                                          duration: const Duration(
                                            milliseconds: 300,
                                          ),
                                          child: IgnorePointer(
                                            ignoring: !isVolumeSliderVisible,
                                            child: SizedBox(
                                              width: 80,
                                              child: SliderTheme(
                                                data: SliderTheme.of(
                                                  context,
                                                ).copyWith(
                                                  trackHeight: 2.0,
                                                  thumbShape:
                                                      const RoundSliderThumbShape(
                                                        enabledThumbRadius: 4.0,
                                                      ),
                                                  overlayShape:
                                                      const RoundSliderOverlayShape(
                                                        overlayRadius: 8.0,
                                                      ),
                                                ),
                                                child: Slider(
                                                  value: currentVolume,
                                                  min: 0.0,
                                                  max: 100.0,
                                                  onChanged:
                                                      (value) => onSetVolume
                                                          ?.call(value),
                                                  activeColor: Colors.green,
                                                  inactiveColor: Colors.grey,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  // Live indicator
                                  if (isLiveStream)
                                    Container(
                                      margin: const EdgeInsets.only(left: 16),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          AnimatedBuilder(
                                            animation: liveAnimation,
                                            builder: (context, child) {
                                              return Container(
                                                width: 8,
                                                height: 8,
                                                decoration: BoxDecoration(
                                                  color: Colors.red.withOpacity(
                                                    liveAnimation.value,
                                                  ),
                                                  shape: BoxShape.circle,
                                                ),
                                              );
                                            },
                                          ),
                                          const SizedBox(width: 6),
                                          const Text(
                                            'LIVE',
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 12,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                ],
                              ),
                              // Main controls (play/pause, forward/rewind)
                              Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  IconButton(
                                    iconSize: 20.0,
                                    icon: const Icon(
                                      Icons.replay_10,
                                      color: Colors.white,
                                    ),
                                    onPressed:
                                        () => onSeek?.call(
                                          currentPosition -
                                              const Duration(seconds: 10),
                                        ),
                                  ),
                                  IconButton(
                                    iconSize: 32.0,
                                    icon: Icon(
                                      isPlaying
                                          ? Icons.pause
                                          : Icons.play_arrow,
                                      color: Colors.white,
                                    ),
                                    onPressed:
                                        () => onPlayButtonPressed?.call(),
                                  ),
                                  IconButton(
                                    iconSize: 20.0,
                                    icon: const Icon(
                                      Icons.forward_10,
                                      color: Colors.white,
                                    ),
                                    onPressed:
                                        () => onSeek?.call(
                                          currentPosition +
                                              const Duration(seconds: 10),
                                        ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          // Right side controls: Subtitles > Settings > PiP > Fullscreen
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              // Subtitles button
                              Builder(
                                builder: (buttonContext) {
                                  return IconButton(
                                    iconSize: 20.0,
                                    icon: const Icon(
                                      Icons.subtitles,
                                      color: Colors.white,
                                    ),
                                    onPressed:
                                        () => onShowSubtitlesMenu?.call(
                                          buttonContext,
                                        ),
                                  );
                                },
                              ),
                              // Settings button
                              Builder(
                                builder: (buttonContext) {
                                  return IconButton(
                                    iconSize: 20.0,
                                    icon: const Icon(
                                      Icons.settings,
                                      color: Colors.white,
                                    ),
                                    onPressed:
                                        () => onShowSettingsMenu?.call(
                                          buttonContext,
                                        ),
                                  );
                                },
                              ),
                              // Picture-in-Picture button
                              IconButton(
                                iconSize: 20.0,
                                icon: const Icon(
                                  Icons.picture_in_picture_alt,
                                  color: Colors.white,
                                ),
                                onPressed:
                                    () => onEnterPictureInPicture?.call(),
                              ),
                              // Fullscreen button
                              IconButton(
                                iconSize: 20.0,
                                icon: Icon(
                                  isFullScreenMode
                                      ? Icons.fullscreen_exit
                                      : Icons.fullscreen,
                                  color: Colors.white,
                                ),
                                onPressed: () => onToggleFullScreen?.call(),
                              ),
                            ],
                          ),
                        ],
                      )
                    else
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // Left side: Volume control and Live indicator
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Volume control
                              MouseRegion(
                                onEnter:
                                    (_) => onToggleVolumeSliderVisibility?.call(
                                      true,
                                    ),
                                onExit:
                                    (_) => onToggleVolumeSliderVisibility?.call(
                                      false,
                                    ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    IconButton(
                                      icon: Icon(
                                        currentVolume == 0
                                            ? Icons.volume_off
                                            : Icons.volume_up,
                                        color: Colors.white,
                                        size: 20.0,
                                      ),
                                      onPressed: () => onToggleMute?.call(),
                                    ),
                                    AnimatedOpacity(
                                      opacity:
                                          isVolumeSliderVisible ? 1.0 : 0.0,
                                      duration: const Duration(
                                        milliseconds: 300,
                                      ),
                                      child: IgnorePointer(
                                        ignoring: !isVolumeSliderVisible,
                                        child: SizedBox(
                                          width: 80,
                                          child: SliderTheme(
                                            data: SliderTheme.of(
                                              context,
                                            ).copyWith(
                                              trackHeight: 2.0,
                                              thumbShape:
                                                  const RoundSliderThumbShape(
                                                    enabledThumbRadius: 4.0,
                                                  ),
                                              overlayShape:
                                                  const RoundSliderOverlayShape(
                                                    overlayRadius: 8.0,
                                                  ),
                                            ),
                                            child: Slider(
                                              value: currentVolume,
                                              min: 0.0,
                                              max: 100.0,
                                              onChanged:
                                                  (value) =>
                                                      onSetVolume?.call(value),
                                              activeColor: Colors.green,
                                              inactiveColor: Colors.grey,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              // Live indicator
                              if (isLiveStream)
                                Container(
                                  margin: const EdgeInsets.only(left: 16),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      AnimatedBuilder(
                                        animation: liveAnimation,
                                        builder: (context, child) {
                                          return Container(
                                            width: 8,
                                            height: 8,
                                            decoration: BoxDecoration(
                                              color: Colors.red.withOpacity(
                                                liveAnimation.value,
                                              ),
                                              shape: BoxShape.circle,
                                            ),
                                          );
                                        },
                                      ),
                                      const SizedBox(width: 6),
                                      const Text(
                                        'LIVE',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                          // Main controls (play/pause, forward/rewind)
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButton(
                                iconSize: 20.0,
                                icon: const Icon(
                                  Icons.replay_10,
                                  color: Colors.white,
                                ),
                                onPressed:
                                    () => onSeek?.call(
                                      currentPosition -
                                          const Duration(seconds: 10),
                                    ),
                              ),
                              IconButton(
                                iconSize: 32.0,
                                icon: Icon(
                                  isPlaying ? Icons.pause : Icons.play_arrow,
                                  color: Colors.white,
                                ),
                                onPressed: () => onPlayButtonPressed?.call(),
                              ),
                              IconButton(
                                iconSize: 20.0,
                                icon: const Icon(
                                  Icons.forward_10,
                                  color: Colors.white,
                                ),
                                onPressed:
                                    () => onSeek?.call(
                                      currentPosition +
                                          const Duration(seconds: 10),
                                    ),
                              ),
                            ],
                          ),
                          // Right side controls: Subtitles > Settings > PiP > Fullscreen
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Subtitles button
                              Builder(
                                builder: (buttonContext) {
                                  return IconButton(
                                    iconSize: 20.0,
                                    icon: const Icon(
                                      Icons.subtitles,
                                      color: Colors.white,
                                    ),
                                    onPressed:
                                        () => onShowSubtitlesMenu?.call(
                                          buttonContext,
                                        ),
                                  );
                                },
                              ),
                              // Settings button
                              Builder(
                                builder: (buttonContext) {
                                  return IconButton(
                                    iconSize: 20.0,
                                    icon: const Icon(
                                      Icons.settings,
                                      color: Colors.white,
                                    ),
                                    onPressed:
                                        () => onShowSettingsMenu?.call(
                                          buttonContext,
                                        ),
                                  );
                                },
                              ),
                              // Picture-in-Picture button
                              IconButton(
                                iconSize: 20.0,
                                icon: const Icon(
                                  Icons.picture_in_picture_alt,
                                  color: Colors.white,
                                ),
                                onPressed:
                                    () => onEnterPictureInPicture?.call(),
                              ),
                              // Fullscreen button
                              IconButton(
                                iconSize: 20.0,
                                icon: Icon(
                                  isFullScreenMode
                                      ? Icons.fullscreen_exit
                                      : Icons.fullscreen,
                                  color: Colors.white,
                                ),
                                onPressed: () => onToggleFullScreen?.call(),
                              ),
                            ],
                          ),
                        ],
                      ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
