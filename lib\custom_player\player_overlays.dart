import 'package:flutter/material.dart';

class PlayerOverlays extends StatelessWidget {
  final bool hasSource;
  final bool isPlaying;
  final bool isLoading;
  final int? bufferingPercentage;
  final String? errorMessage;
  final bool showPlayPauseSplash;
  final bool showVolumeSplash;
  final String volumeSplashText;
  final bool showCatSound;
  final String catSoundText;
  final bool
  areControlsVisible; // To hide big play button when controls are visible
  final VoidCallback? onPlayButtonPressed;

  const PlayerOverlays({
    super.key,
    required this.hasSource,
    required this.isPlaying,
    required this.isLoading,
    this.bufferingPercentage,
    this.errorMessage,
    required this.showPlayPauseSplash,
    required this.showVolumeSplash,
    required this.volumeSplashText,
    required this.showCatSound,
    required this.catSoundText,
    required this.areControlsVisible,
    this.onPlayButtonPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Black transparent mask
        AnimatedOpacity(
          opacity: areControlsVisible ? 1.0 : 0.0,
          duration: const Duration(milliseconds: 300),
          child: IgnorePointer(
            ignoring: !areControlsVisible,
            child: Container(color: Colors.black.withValues(alpha: 0.3 * 255)),
          ),
        ),
        // Default thumbnail when no video source
        if (!hasSource)
          Container(
            color: Colors.grey[900],
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.video_library_outlined,
                    size: 80,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'No video loaded',
                    style: TextStyle(color: Colors.grey, fontSize: 18),
                  ),
                ],
              ),
            ),
          ),
        // Big triangle play button - shows when video is loaded but not playing AND controls are not visible
        if (hasSource)
          if (!isPlaying && !areControlsVisible)
            Center(
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.black.withValues(alpha: 0.6 * 255),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.8 * 255),
                    width: 2,
                  ),
                ),
                child: IconButton(
                  onPressed: () {
                    onPlayButtonPressed?.call();
                  },
                  icon: const Icon(
                    Icons.play_arrow,
                    color: Colors.white,
                    size: 40.0,
                  ),
                ),
              ),
            ),
        // Play/Pause splash effect
        if (showPlayPauseSplash)
          Center(
            child: Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.6 * 255),
                borderRadius: BorderRadius.circular(50),
              ),
              child: Icon(
                isPlaying ? Icons.pause : Icons.play_arrow,
                color: Colors.white,
                size: 50,
              ),
            ),
          ),
        // Volume splash effect
        if (showVolumeSplash)
          Center(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.6 * 255),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                volumeSplashText,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        // Cat sound splash effect
        if (showCatSound)
          Center(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
              decoration: BoxDecoration(
                color: Colors.pink.withValues(alpha: 0.8 * 255),
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.6 * 255),
                  width: 2,
                ),
              ),
              child: Text(
                catSoundText,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      offset: Offset(1, 1),
                      blurRadius: 3,
                      color: Colors.black54,
                    ),
                  ],
                ),
              ),
            ),
          ),
        // Loading indicator when opening a stream (only if no error message and not playing yet)
        if (isLoading && errorMessage == null && !isPlaying)
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  strokeWidth: 6,
                ),
                const SizedBox(height: 16),
                const Text(
                  'Opening stream...',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        // Buffering indicator with percentage (only if no error message and already playing)
        if (isLoading &&
            bufferingPercentage != null &&
            errorMessage == null &&
            isPlaying)
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  value: bufferingPercentage! / 100,
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                  strokeWidth: 6,
                ),
                const SizedBox(height: 16),
                Text(
                  'Buffering: $bufferingPercentage%',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        // Error message display
        if (errorMessage != null)
          Center(
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7 * 255),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.error_outline, color: Colors.red, size: 50),
                  const SizedBox(height: 10),
                  Text(
                    errorMessage!,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 10),
                  const Text(
                    'Please try a different stream or check your network connection.',
                    textAlign: TextAlign.center,
                    style: TextStyle(color: Colors.white70, fontSize: 14),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }
}
