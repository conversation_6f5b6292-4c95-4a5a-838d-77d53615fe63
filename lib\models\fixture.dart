import 'package:flutter/material.dart';

class FixtureMatch {
  final String matchId;
  final String time;
  final String title;
  final String teams;
  final String url;
  final List<String> channels;
  final String? homeTeamFlagUrl; // New field for home team flag
  final String? awayTeamFlagUrl; // New field for away team flag

  FixtureMatch({
    required this.matchId,
    required this.time,
    required this.title,
    required this.teams,
    required this.url,
    required this.channels,
    this.homeTeamFlagUrl, // Make it optional for now
    this.awayTeamFlagUrl, // Make it optional for now
  });

  factory FixtureMatch.fromJson(Map<String, dynamic> json) {
    debugPrint(
      'Parsing FixtureMatch from JSON: $json',
    ); // Moved debug print here
    return FixtureMatch(
      matchId: json['match_id'] ?? '',
      time: json['time'] ?? '',
      title: json['title'] ?? '',
      teams: json['teams'] ?? '',
      url: json['url'] ?? '',
      channels: List<String>.from(json['channels'] ?? []),
      homeTeamFlagUrl: json['home_team_flag_url'], // Parse new field
      awayTeamFlagUrl: json['away_team_flag_url'], // Parse new field
    );
  }

  // Parse team names and score from teams string
  Map<String, dynamic> get parsedTeams {
    final regex = RegExp(r'^(.+?)(\d+)\s*-\s*(\d+)(.+?)$');
    final match = regex.firstMatch(teams);

    if (match != null) {
      return {
        'homeTeam': match.group(1)?.trim() ?? '',
        'homeScore': int.tryParse(match.group(2) ?? '') ?? 0,
        'awayScore': int.tryParse(match.group(3) ?? '') ?? 0,
        'awayTeam': match.group(4)?.trim() ?? '',
        'hasScore': true,
      };
    } else {
      // No score, probably upcoming match
      final parts = teams.split(' vs ');
      return {
        'homeTeam': parts.isNotEmpty ? parts[0].trim() : '',
        'awayTeam': parts.length > 1 ? parts[1].trim() : '',
        'homeScore': 0,
        'awayScore': 0,
        'hasScore': false,
      };
    }
  }

  // Get match status based on score and time
  MatchStatus get status {
    final parsed = parsedTeams;
    if (parsed['hasScore']) {
      return MatchStatus.finished;
    }

    // Parse time to determine if match is live or upcoming
    final now = DateTime.now();
    final matchTime = _parseMatchTime(time);

    if (matchTime != null) {
      final difference = matchTime.difference(now);
      // If match time is within 15 minutes before or 2 hours after, consider it live
      if (difference.inMinutes >= -15 && difference.inMinutes <= 120) {
        return MatchStatus.live;
      } else if (difference.isNegative) {
        return MatchStatus.finished;
      } else {
        return MatchStatus.upcoming;
      }
    }

    return MatchStatus.upcoming;
  }

  DateTime? _parseMatchTime(String timeStr) {
    try {
      final parts = timeStr.split(':');
      if (parts.length == 2) {
        final hour = int.parse(parts[0]);
        final minute = int.parse(parts[1]);
        final now = DateTime.now();
        return DateTime(now.year, now.month, now.day, hour, minute);
      }
    } catch (e) {
      // Ignore parsing errors
    }
    return null;
  }

  Duration? get timeUntilStart {
    final matchTime = _parseMatchTime(time);
    if (matchTime != null && status == MatchStatus.upcoming) {
      final now = DateTime.now();
      final difference = matchTime.difference(now);
      return difference.isNegative ? null : difference;
    }
    return null;
  }
}

class FixtureLeague {
  final String leagueName;
  final String flagClass;
  final String leagueUrl;
  final List<FixtureMatch> matches;

  FixtureLeague({
    required this.leagueName,
    required this.flagClass,
    required this.leagueUrl,
    required this.matches,
  });

  factory FixtureLeague.fromJson(Map<String, dynamic> json) {
    return FixtureLeague(
      leagueName: json['league_name'] ?? '',
      flagClass: json['flag_class'] ?? '',
      leagueUrl: json['league_url'] ?? '',
      matches:
          (json['matches'] as List<dynamic>?)
              ?.map((match) => FixtureMatch.fromJson(match))
              .toList() ??
          [],
    );
  }

  String get flagUrl {
    final formattedFlagClass = flagClass.toLowerCase().replaceAll(' ', '-');
    return 'https://cdn.livesoccertv.com/images/flags/48/$formattedFlagClass.png';
  }
}

enum MatchStatus { upcoming, live, finished }
