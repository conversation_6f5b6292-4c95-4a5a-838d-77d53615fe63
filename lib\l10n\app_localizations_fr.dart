// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get appTitle => 'TV pour Chats';

  @override
  String get helloWorld => 'Bonjour le Monde';

  @override
  String welcomeMessage(Object userName) {
    return 'Bienvenue, $userName!';
  }

  @override
  String get noSourcesAvailable => 'Aucune source disponible pour cette chaîne';

  @override
  String get liveTab => 'En direct';

  @override
  String get fixturesTab => 'Matchs';

  @override
  String get favoritesTab => 'Favoris';

  @override
  String get channelsTitle => 'Chaînes';

  @override
  String get channelListPlaceholder => 'La liste des chaînes sera ici';

  @override
  String get language_ar => 'Arabe';

  @override
  String get language_en => 'Anglais';

  @override
  String get language_es => 'Espagnol';

  @override
  String get language_fr => 'Français';

  @override
  String get language_pl => 'Polonais';

  @override
  String get disclaimerTitle => 'Avertissement';

  @override
  String get disclaimerText => 'Cette application n\'héberge, ne stocke ni ne contrôle aucun flux vidéo ou logo affiché. Tous les flux proviennent de liens IPTV accessibles au public maintenus par le projet IPTV-org. L\'application est uniquement à des fins personnelles et informatives. Tous les logos et le contenu des chaînes sont la propriété de leurs propriétaires respectifs.';

  @override
  String appVersion(Object version) {
    return 'Version de l\'application : $version';
  }

  @override
  String get settingsTitle => 'Paramètres';

  @override
  String get appLanguage => 'Langue de l\'application';

  @override
  String get adsEnabled => 'Afficher les publicités';

  @override
  String get enabled => 'Activé';

  @override
  String get disabled => 'Désactivé';

  @override
  String get updatingServersData => 'Mise à jour des données des serveurs...';

  @override
  String get serversUpdateSuccess => 'Données des serveurs mises à jour avec succès !';

  @override
  String get failedToUpdateServers => 'Échec de la mise à jour des données des serveurs.';

  @override
  String get serversUpdateCancelled => 'Mise à jour des données des serveurs annulée.';

  @override
  String nextServerUpdate(Object date) {
    return 'Prochaine mise à jour du serveur : $date';
  }

  @override
  String get alreadyUpToDate => 'Déjà à jour !';

  @override
  String get serversUpToDate => 'Servers data is up to date.';

  @override
  String iptvDataLastUpdate(Object date) {
    return 'IPTV Data Last Update: $date';
  }
}
